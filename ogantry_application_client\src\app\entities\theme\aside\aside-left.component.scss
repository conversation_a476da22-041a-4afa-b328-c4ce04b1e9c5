:host {
  .aside {
    height: 100%;

    .aside-menu {
      margin: 0;
    }

    .menu-icon {
      margin-left: 25px;
    }
  }

  // fixed text line break issue on minimized aside hover
  .menu-text {
    white-space: nowrap;
    position: relative;
    transition: all 0.3s ease;
  }
}
@media (min-width: 992px) {
  kt-brand {
    margin: 30px 0;
  }
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-icon {
  color: white;
}

.aside-menu .menu-nav > .menu-item.menu-item-open > .menu-link .menu-arrow {
  padding-left: 15px;
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-arrow {
  color: white;
  padding-right: 15px;
}

// Enhanced styling for main menu items (root level with submenus)
.aside-menu .menu-nav > .menu-item > .menu-link {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 2px 8px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(3px);

    .menu-text {
      color: #d3a75d;
    }

    .svg-icon {
      filter: brightness(1.2);
    }
  }

  &:focus {
    outline: 2px solid rgba(211, 167, 93, 0.5);
    outline-offset: 2px;
  }
}

// Enhanced styling for submenu items (the actual clickable links)
.aside-menu .menu-nav > .menu-item > .menu-submenu .menu-subnav > .menu-item > .menu-link {
  padding: 0 25px 0 51px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin: 1px 4px;
  position: relative;

  // Add underline effect for submenu items
  .menu-text {
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      background-color: #d3a75d;
      transition: width 0.3s ease;
    }
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.08);
    transform: translateX(2px);

    .menu-text {
      color: #d3a75d;

      &::after {
        width: 100%;
      }
    }
  }

  &:focus {
    outline: 2px solid rgba(211, 167, 93, 0.3);
    outline-offset: 1px;
  }
}

// ALWAYS VISIBLE indicators for submenu items that have sub-links (nested submenus)
// This targets items with .menu-item-submenu class which is only added when item.submenu exists
.aside-menu .menu-nav > .menu-item > .menu-submenu .menu-subnav > .menu-item.menu-item-submenu > .menu-link {
  // ALWAYS VISIBLE background and border
  background: linear-gradient(90deg, transparent 0%, rgba(211, 167, 93, 0.15) 100%) !important;
  border-left: 4px solid #d3a75d !important;
  padding-left: 48px !important;
  position: relative !important;

  // ALWAYS VISIBLE expandable arrow indicator
  &::before {
    content: '▶' !important;
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #d3a75d !important;
    font-size: 14px !important;
    font-weight: bold !important;
    opacity: 1 !important;
    z-index: 10 !important;
    transition: all 0.3s ease !important;
    display: block !important;
  }

  // ALWAYS VISIBLE folder icon to indicate container
  &::after {
    content: '📁' !important;
    position: absolute !important;
    left: 22px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 12px !important;
    opacity: 1 !important;
    z-index: 10 !important;
    display: block !important;
  }

  .menu-text {
    font-weight: 600 !important;
    color: #ffffff !important;
    padding-left: 8px !important;

    // Override any conflicting underline effects
    &::after {
      display: none !important;
    }
  }

  &:hover {
    background: linear-gradient(90deg, transparent 0%, rgba(211, 167, 93, 0.25) 100%) !important;
    border-left-color: #ffffff !important;

    &::before {
      transform: translateY(-50%) scale(1.1) !important;
      color: #ffffff !important;
    }

    &::after {
      opacity: 1 !important;
    }
  }

  // When expanded
  &.menu-item-open::before,
  &.menu-item-active::before {
    transform: translateY(-50%) rotate(90deg) !important;
    color: #ffffff !important;
  }
}

// Ensure regular submenu items (no sub-links) stay normal - NO special indicators
.aside-menu .menu-nav > .menu-item > .menu-submenu .menu-subnav > .menu-item:not(.menu-item-submenu) > .menu-link {
  // Explicitly ensure NO special styling for items without sub-links
  background: transparent !important;
  border-left: none !important;
  padding-left: 51px !important; // Original padding

  // Ensure NO pseudo-elements for regular items
  &::before,
  &::after {
    display: none !important;
  }

  .menu-text {
    font-weight: 400 !important; // Normal weight for direct links
    color: #ffffff !important;

    // Keep the original underline effect for regular items
    &::after {
      content: '' !important;
      position: absolute !important;
      bottom: -2px !important;
      left: 0 !important;
      width: 0 !important;
      height: 2px !important;
      background-color: #d3a75d !important;
      transition: width 0.3s ease !important;
      display: block !important;
    }
  }

  &:hover {
    .menu-text {
      color: #d3a75d !important;

      &::after {
        width: 100% !important;
      }
    }
  }
}

// Alternative: Small arrow icon for nested submenu indicators
.aside-menu .menu-nav > .menu-item > .menu-submenu .menu-subnav > .menu-item.menu-item-submenu > .menu-link .menu-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #9899ac;
  font-size: 8px;
  transition: all 0.3s ease;

  &:before {
    content: '\f105'; // FontAwesome right arrow
    font-family: 'Font Awesome 5 Free', 'Ki';
    font-weight: 900;
  }

  .menu-link:hover & {
    color: #d3a75d;
    transform: translateY(-50%) scale(1.1);
  }
}
.svg-icon {
  width: 20px;
  margin-left: 15px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.manage-people {
  width: 23px !important;
  margin-right: 5px !important;
}

@-moz-document url-prefix() {
  .menu-link > .svg-icon {
    margin-left: 4px !important;
    margin-right: 2px !important;
    padding: 10px;
  }
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-arrow:before {
  transform: rotate(90deg);
  font-family: 'Ki';
}

.aside-menu .menu-nav > .menu-item.menu-item-here-1 > .menu-link .menu-text {
  color: #d3a75d;
}

.aside-menu .menu-nav > .menu-item.menu-item-open > .menu-link .menu-arrow {
  transform: rotate(180deg);
  font-family: 'Ki';
}
.aside-menu .menu-nav > .menu-item.menu-item-here-1 > .menu-link .menu-arrow {
  color: #d3a75d;
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-text {
  color: #ffffff;
}

.svg-icon.svg-icon-sm {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin-left: 18px;
}

.profile {
  display: flex;
  align-items: center;
  height: 70px;
  background-color: #45386d;
}

.filter-list {
  color: #9899ac;
  list-style: none;
  font-size: larger;
  font-weight: 500;
  padding: 10px;
  cursor: pointer;
}

.filter-list:hover {
  color: #ffffff;
}

.listOfFilter {
  margin: 10px;
  padding-inline-start: 0px;
}

.menu-arrow-right:before {
  font-family: 'ki' !important;
}

::ng-deep .filter {
  &.p-overlaypanel-flipped:before,
  &.p-overlaypanel-flipped:after {
    visibility: hidden;
  }
  &.p-overlaypanel {
    background: #4b3f72;
    left: 0px !important;
    margin-left: 265px !important;
    margin-top: -45px !important;
  }
  &.p-overlaypanel-content {
    width: 200px !important;
    padding: 0 !important;
  }
}
