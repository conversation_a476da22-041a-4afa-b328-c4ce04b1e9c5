// Angular
import { Injectable } from '@angular/core';
// RxJS
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
// Object path
import * as objectPath from 'object-path';
// Services
import { MenuConfigService } from './menu-config.service';
import { AuthService } from '@auth/auth.service';

@Injectable()
export class MenuAsideService {
  // Public properties
  menuList$: BehaviorSubject<object> = new BehaviorSubject<object>([]);

  /**
   * Service constructor
   *
   * @param menuConfigService: MenuConfigService
   */
  constructor(private readonly menuConfigService: MenuConfigService, private readonly authService: AuthService) {
    this.loadMenu();
    this.menuConfigService.onConfigUpdated$.subscribe(() => {
      this.loadMenu();
    });
    this.authService.isClearMenuList$.subscribe((isClear) => {
      if (isClear) {
        this.menuList$.next([]);
      }
    });
  }

  async loadMenu() {
    // get menu list
    const menuItems: any[] = objectPath.get(this.menuConfigService.getMenus(), 'aside.items');
    const authorizedMenuItems = await this.loadAuthorizedMenuConfigs(menuItems);
    this.menuList$.next(authorizedMenuItems);
  }

  private async loadAuthorizedMenuConfigs(menuItems) {
    const authorizedMenus = [];
    for (const menu of menuItems) {
      const authorisedSubMenus = await this.processSubMenus(menu.submenu);
      if (authorisedSubMenus.length) {
        authorizedMenus.push({ ...menu, submenu: authorisedSubMenus });
      }
    }
    return authorizedMenus;
  }

  // Recursive method to handle nested submenus
  private async processSubMenus(subMenus: any[]): Promise<any[]> {
    const authorisedSubMenus = [];
    for (const submenu of subMenus) {
      if (await this.authService.isPermittedAction(submenu?.permissionModules)) {
        // If this submenu has its own nested submenus, process them recursively
        if (submenu.submenu && submenu.submenu.length > 0) {
          const nestedSubMenus = await this.processSubMenus(submenu.submenu);
          if (nestedSubMenus.length > 0) {
            authorisedSubMenus.push({ ...submenu, submenu: nestedSubMenus });
          }
        } else {
          // Regular submenu item without nested submenus
          authorisedSubMenus.push(submenu);
        }
      }
    }
    return authorisedSubMenus;
  }
}
